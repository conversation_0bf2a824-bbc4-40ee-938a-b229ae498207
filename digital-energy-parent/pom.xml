<?xml version="1.0" encoding="UTF-8" ?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zte.oes.dexcloud</groupId>
        <artifactId>dexcloud-springboot-parent</artifactId>
        <version>3.24.40.06P01</version>
        <relativePath/>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.zte.uedm.digital.energy</groupId>
    <artifactId>digital-energy-parent</artifactId>
    <version>${digital.energy.service.version}</version>
    <packaging>pom</packaging>

    <properties>
        <!--		依赖uedm的common 版本号（暂时复用uedm的kafak,db，redis等，未做迁移）-->
        <revision>V16.25.40.07</revision>
        <!--		common 版本号-->
        <digital.energy.commons.version>0.0.1-SNAPSHOT</digital.energy.commons.version>
        <!--		业务 版本号-->
        <digital.energy.service.version>0.0.1-SNAPSHOT</digital.energy.service.version>
        <!--		okiv5 版本号-->
        <chart.version>16.24.40-11</chart.version>
        <target.outputdir-cloud>target/outputdir-cloud</target.outputdir-cloud>
        <!--		<target.cloud>target/cloud</target.cloud>-->
        <uedm_redis>redis1</uedm_redis>
        <uedm_redis_cpaas>redis</uedm_redis_cpaas>
        <uedm_orch_name>commsrv_inner_redis_cluster_bp</uedm_orch_name>
        <uedm_orch_name_cpaas>commsrv_internal_redis_bp</uedm_orch_name_cpaas>

        <dexcloud-springboot.version>3.24.40.06P01</dexcloud-springboot.version>
        <zenap-iag.version>v3.08.01</zenap-iag.version>
        <tenant.id>uedm</tenant.id>
        <docker.tag>${revision}</docker.tag>
        <uedm.version>${revision}</uedm.version>
        <uedm-common.version>${revision}-SNAPSHOT</uedm-common.version>
        <image.version>${docker.tag}</image.version>
        <mybatis.plus.version>3.5.3.1</mybatis.plus.version>
        <spring.version>5.3.39</spring.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <kafka_2.12.version>3.4.0</kafka_2.12.version>
        <jetty.version>9.4.54.v20240208</jetty.version>
        <jwt.version>3.2.0</jwt.version>
        <netty.version>4.1.111.Final</netty.version>
        <jersey.guava.version>2.16</jersey.guava.version>
        <immutables.version>2.1.12</immutables.version>
        <rxjava2.version>2.2.21</rxjava2.version>
        <!-- 提供给dependency镜像所有 -->
        <dexcloud.tenant.id>${zenap.server.tenant.id}</dexcloud.tenant.id>
        <dexcloud.docker.tag>${zenap.javaaf.docker.tag}</dexcloud.docker.tag>

        <docker.maintainer><![CDATA["hu rui" <<EMAIL>>]]></docker.maintainer>
        <docker.workdir>/home/<USER>/docker.workdir>

        <pact.version>3.6.14</pact.version>

        <output.dir>target/outputdir</output.dir>
        <release.dir>target/release</release.dir>

        <!--flowable-->
        <flowable.version>6.8.1</flowable.version>

        <!-- maven plugins -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version>
        <jacoco-maven-plugin.version>0.8.8</jacoco-maven-plugin.version>
        <maven-surefire-plugin.version>3.0.0-M9</maven-surefire-plugin.version>
    </properties>
    <profiles>
        <!-- Jenkins by default defines a property BUILD_NUMBER which is used to
            enable the profile... -->
        <profile>
            <id>jenkins</id>
            <activation>
                <property>
                    <name>env.BUILD_NUMBER</name>
                </property>
            </activation>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-autoconfigure</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- flowable -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter</artifactId>
                <version>${flowable.version}</version>
            </dependency>


            <dependency>
                <groupId>org.immutables</groupId>
                <artifactId>value</artifactId>
                <version>${immutables.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.bundles.repackaged</groupId>
                <artifactId>jersey-guava</artifactId>
                <version>${jersey.guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>sdk-all-dependency</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.zte.ums.zenap.sdk.forwardinfo</groupId>
                <artifactId>zenap-forwardinfo-client</artifactId>
                <version>${dexcloud-springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter-redis-redisson</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.projectreactor</groupId>
                        <artifactId>reactor-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-buffer</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-resolver-dns</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-handler</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka_2.12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka-clients</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter-kafka</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka_2.12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka-clients</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka_2.12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka-clients</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-http</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-jetty</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-test</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish.jersey.core</groupId>
                        <artifactId>jersey-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter-limit</artifactId>
                <version>${dexcloud-springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-aop</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-expression</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-client</artifactId>
                <version>2.34</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-common</artifactId>
                <version>2.34</version>
            </dependency>
            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter-msb</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-http</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-jetty</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-test</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter-rpc-retrofit</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka_2.12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka-clients</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish.jersey.core</groupId>
                        <artifactId>jersey-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.retrofit2</groupId>
                        <artifactId>retrofit</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>log4j-over-slf4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>jul-to-slf4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter-web</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-buffer</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-resolver-dns</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-handler</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-http</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-jetty</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-test</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish.jersey.core</groupId>
                        <artifactId>jersey-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish.jersey.core</groupId>
                        <artifactId>jersey-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish</groupId>
                        <artifactId>jakarta.el</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>jakarta.el</artifactId>
                <version>4.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>2.11.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.12.0</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-dns</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-epoll</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-http</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jetty</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>jakarta.websocket</groupId>
                        <artifactId>jakarta.websocket-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat.embed</groupId>
                        <artifactId>tomcat-embed-el</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.3.8.RELEASE</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>net.minidev</groupId>
                        <artifactId>json-smart</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter-db-jdbc</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka_2.12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kafka</groupId>
                        <artifactId>kafka-clients</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>3.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>2.0.7</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.7</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-doclite-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>ft-starter</artifactId>
                <version>${uedm-common.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-rm-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-fm-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-pma-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-south-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-security-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-cfgcenter-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-canvas-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-basis-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-ftp-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-file-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-util-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-caffeine-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-compute-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-config-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-db-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-kafka-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-backup-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-redis-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-thread-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-license-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-log-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-mp-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-bm-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-sm-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-configuration</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-original-point-data-manager</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-util</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-service</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>caffeine-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>redis-service-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>offline-task-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>kafka-consumer-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>kafka-producer-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-notification-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-msb-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>common-ft-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>license-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>pma-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>log-trace-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>fm-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>fm-spring-boot-starter-autoconfigure</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>data-backup-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jdom</groupId>
                        <artifactId>jdom</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.2.13</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.2.13</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>ch.qos.logback</groupId>-->
<!--                <artifactId>logback-access</artifactId>-->
<!--                <version>1.2.13</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>data-transfer-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>uedm-starter-db-install</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>imop-cma-log-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>httpclient-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zte.uedm</groupId>
                <artifactId>httpsclient-spring-boot-starter</artifactId>
                <version>${uedm-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.snmp4j</groupId>
                <artifactId>snmp4j</artifactId>
                <version>2.8.12</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>2.1.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.2.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.2.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-full</artifactId>
                <version>5.2.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-lite</artifactId>
                <version>5.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.17.0</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>1.4.199</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.9</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.lowagie</groupId>
                <artifactId>itextasian</artifactId>
                <version>1.5.2</version>
            </dependency>
            <dependency>
                <groupId>org.jfree</groupId>
                <artifactId>jfreechart</artifactId>
                <version>1.0.19</version>
            </dependency>
            <dependency>
                <groupId>com.lowagie</groupId>
                <artifactId>itext</artifactId>
                <version>2.1.7</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.15.4</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>1.68</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.68</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.15.4</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>20231013</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.15.4</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.2</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-json</artifactId>
                <version>2.5.15</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>fluent-hc</artifactId>
                <version>4.5.13</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.14</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.13</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.38</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>2.5.15</version>
            </dependency>
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>1.4.7</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk8</artifactId>
                <version>1.6.0</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>0.9.1</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>0.11.5</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>0.11.5</version>
                <scope>runtime</scope>
            </dependency>
            <!--mapstruct核心-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>1.5.2.Final</version>
            </dependency>
            <!--mapstruct编译-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>1.5.2.Final</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>0.2.0</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>0.11.5</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.eweb4j</groupId>
                <artifactId>fel</artifactId>
                <version>0.8</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>5.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr-runtime</artifactId>
                <version>3.5.2</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.mqttv5.client</artifactId>
                <version>1.2.5</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.4</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/commons-logging/commons-logging -->
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.2.0</version>
            </dependency>

            <dependency>
                <groupId>au.com.dius</groupId>
                <artifactId>pact-jvm-provider-junit_2.12</artifactId>
                <version>${pact.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>fluent-hc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-json</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                        <artifactId>jackson-dataformats-binary</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                        <artifactId>jackson-dataformat-cbor</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>au.com.dius</groupId>
                <artifactId>pact-jvm-provider-spring_2.12</artifactId>
                <version>${pact.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ibm.icu</groupId>
                        <artifactId>icu4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.dataformat</groupId>
                        <artifactId>jackson-dataformat-cbor</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.amazonaws</groupId>
                        <artifactId>aws-java-sdk-kms</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.amazonaws</groupId>
                        <artifactId>aws-java-sdk-s3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.amazonaws</groupId>
                        <artifactId>aws-java-sdk-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.dmfs</groupId>
                        <artifactId>rfc3986-uri</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId> com.github.mifmif</groupId>
                        <artifactId>generex</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <artifactId>asm</artifactId>
                <groupId>org.ow2.asm</groupId>
                <version>7.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test</artifactId>
                <version>2.3.8.RELEASE</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.gavaghan</groupId>
                <artifactId>geodesy</artifactId>
                <version>1.1.3</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>24.0.0</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-haproxy</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-http2</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-http</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-memcache</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-mqtt</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-socks</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-stomp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-xml</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-handler-proxy</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport-native-epoll</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport-rxtx</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport-sctp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport-udt</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.vandeseer</groupId>
                <artifactId>easytable</artifactId>
                <version>0.7.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.26</version>
            </dependency>
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>0.4.17</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.ghgande</groupId>
                <artifactId>j2mod</artifactId>
                <version>3.2.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fazecast</groupId>
                        <artifactId>jSerialComm</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>ch.ethz.ganymed</groupId>
                <artifactId>ganymed-ssh2</artifactId>
                <version>262</version>
            </dependency>
            <dependency>
                <groupId>org.apache.sshd</groupId>
                <artifactId>sshd-core</artifactId>
                <version>2.13.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.sshd</groupId>
                <artifactId>sshd-sftp</artifactId>
                <version>2.13.2</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.55</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.15.3</version>
            </dependency>
            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring-boot-starter-basic</artifactId>
                <version>6.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-email</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-bpmn-layout</artifactId>
                <version>6.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.5.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.9.0</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-util</artifactId>
                <version>${jetty.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-util-ajax</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-jmx</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>apache-jsp</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-io</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-servlets</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-continuation</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-webapp</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-xml</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-servlet</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-security</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-server</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.websocket</groupId>
                <artifactId>websocket-server</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.websocket</groupId>
                <artifactId>websocket-common</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.websocket</groupId>
                <artifactId>websocket-api</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.websocket</groupId>
                <artifactId>websocket-client</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-client</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.websocket</groupId>
                <artifactId>websocket-servlet</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.websocket</groupId>
                <artifactId>javax-websocket-server-impl</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-annotations</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-plus</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.websocket</groupId>
                <artifactId>javax-websocket-client-impl</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>apache-jstl</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-alpn-client</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-alpn-java-client</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-alpn-java-server</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-alpn-openjdk8-client</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-alpn-openjdk8-server</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-alpn-conscrypt-client</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-alpn-conscrypt-server</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-alpn-server</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-ant</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.cdi</groupId>
                <artifactId>cdi-core</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.cdi</groupId>
                <artifactId>cdi-servlet</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-deploy</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-distribution</artifactId>
                <version>9.4.44.v20210927</version>
                <type>zip</type>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-distribution</artifactId>
                <version>9.4.44.v20210927</version>
                <type>tar.gz</type>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.fcgi</groupId>
                <artifactId>fcgi-client</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.fcgi</groupId>
                <artifactId>fcgi-server</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.gcloud</groupId>
                <artifactId>jetty-gcloud-session-manager</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-home</artifactId>
                <version>9.4.44.v20210927</version>
                <type>zip</type>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-home</artifactId>
                <version>9.4.44.v20210927</version>
                <type>tar.gz</type>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.http2</groupId>
                <artifactId>http2-client</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.http2</groupId>
                <artifactId>http2-common</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.http2</groupId>
                <artifactId>http2-hpack</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.http2</groupId>
                <artifactId>http2-http-client-transport</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.http2</groupId>
                <artifactId>http2-server</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-http-spi</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-infinispan</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-hazelcast</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-jaas</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-jaspi</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-jndi</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.memcached</groupId>
                <artifactId>jetty-memcached-sessions</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-nosql</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.osgi</groupId>
                <artifactId>jetty-osgi-boot</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.osgi</groupId>
                <artifactId>jetty-osgi-boot-jsp</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.osgi</groupId>
                <artifactId>jetty-osgi-boot-warurl</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.osgi</groupId>
                <artifactId>jetty-httpservice</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-proxy</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-quickstart</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-rewrite</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-spring</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-unixsocket</artifactId>
                <version>9.4.44.v20210927</version>
            </dependency>

            <dependency>
                <groupId>com.zte.oes.dexcloud</groupId>
                <artifactId>dexcloud-springboot-starter-configcenter</artifactId>
                <version>${dexcloud-springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.security</groupId>
                        <artifactId>spring-security-crypto</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-aop</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>2.7.18</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.web</groupId>
                <artifactId>javax.el</artifactId>
                <version>2.2.6</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.4.3</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>1.6.20</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <type>jar</type>
                <version>2.11.4</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>5.12.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.svnkit</groupId>
                        <artifactId>svnkit-jna</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>1.7.32</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.12.23</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.4</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.32</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.17.0</version>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>3.1.4</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>1.9.9.1</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.32</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>19.3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ucp</artifactId>
                <version>19.3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.ha</groupId>
                <artifactId>simplefan</artifactId>
                <version>19.3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.security</groupId>
                <artifactId>osdt_core</artifactId>
                <version>19.3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.security</groupId>
                <artifactId>osdt_cert</artifactId>
                <version>19.3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.security</groupId>
                <artifactId>oraclepki</artifactId>
                <version>19.3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.ha</groupId>
                <artifactId>ons</artifactId>
                <version>19.3.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_2.12</artifactId>
                <version>${kafka_2.12.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bitbucket.b_c</groupId>
                        <artifactId>jose4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-metadata</artifactId>
                <version>${kafka_2.12.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-raft</artifactId>
                <version>${kafka_2.12.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-server-common</artifactId>
                <version>${kafka_2.12.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-storage-api</artifactId>
                <version>${kafka_2.12.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-storage</artifactId>
                <version>${kafka_2.12.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 兼容运行 JUnit 4 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- JUnit Vintage Engine 用于兼容运行 JUnit 4 -->
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>



    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <testFailureIgnore>true</testFailureIgnore>
                        <forkCount>1</forkCount>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>check</id>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>prepare-package</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <rules>
                            <rule implementation="org.jacoco.maven.RuleConfiguration">
                                <element>BUNDLE</element>
                                <limits>
                                    <limit implementation="org.jacoco.report.check.Limit">
                                        <counter>INSTRUCTION</counter>
                                        <value>COVEREDRATIO</value>
                                        <minimum>0.00</minimum>
                                    </limit>
                                </limits>
                            </rule>
                        </rules>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- 统一修改项目版本插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.32</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.2.Final</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>2.22.1</version>
                <configuration>
                    <skipTests>false</skipTests>
                    <argLine>${argLine} -Dfile.encoding=UTF-8</argLine>
                    <aggregate>true</aggregate>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>report-only</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <distributionManagement>
        <repository>
            <id>uedm-release-maven</id>
            <name>uedm-release-maven</name>
            <url>https://artnj.zte.com.cn:443/artifactory/uedm-release-maven</url>
            <uniqueVersion>false</uniqueVersion>
        </repository>
        <snapshotRepository>
            <uniqueVersion>false</uniqueVersion>
            <id>uedm-snapshot-maven</id>
            <name>uedm-snapshot-maven</name>
            <url>https://artnj.zte.com.cn:443/artifactory/uedm-snapshot-maven</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>uedm-release-maven</id>
            <url>https://artnj.zte.com.cn:443/artifactory/uedm-release-maven/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>uedm-snapshot-maven</id>
            <url>https://artnj.zte.com.cn:443/artifactory/uedm-snapshot-maven/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
</project>
