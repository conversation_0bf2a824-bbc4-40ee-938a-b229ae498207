-- 权限管理系统数据库表结构
-- 支持幂等性执行

-- 1. 菜单表
CREATE TABLE IF NOT EXISTS t_menu (
    menu_id VARCHAR(100) PRIMARY KEY,
    menu_name VARCHAR(100) NOT NULL,
    parent_id BIGINT,
    menu_path VARCHAR(200),
    sort_order INTEGER,
    menu_level INTEGER,
    create_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建菜单表索引
CREATE INDEX IF NOT EXISTS idx_menu_parent_id ON t_menu(parent_id);

-- 2. 角色表
CREATE TABLE IF NOT EXISTS t_role (
    role_id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(20) NOT NULL,
    role_description VARCHAR(200),
    role_type VARCHAR(20) DEFAULT 'CUSTOM',
    create_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    update_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50)
);

-- 创建角色表索引
CREATE INDEX IF NOT EXISTS idx_role_type ON t_role(role_type);

-- 3. 用户表
CREATE TABLE IF NOT EXISTS t_user (
    user_id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    user_code VARCHAR(50) NOT NULL UNIQUE,
    organization VARCHAR(200),
    create_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    update_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50)
);

-- 创建用户表索引
CREATE INDEX IF NOT EXISTS idx_user_code ON t_user(user_code);

-- 4. 用户组表
CREATE TABLE IF NOT EXISTS t_user_group (
    user_group_id BIGSERIAL PRIMARY KEY,
    group_name VARCHAR(50) NOT NULL,
    group_description TEXT,
    create_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    update_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(50)
);

-- 创建用户组表索引（暂无需要索引的字段）

-- 5. 角色菜单关联表
CREATE TABLE IF NOT EXISTS t_role_menu (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    menu_id BIGINT NOT NULL,
    create_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建角色菜单关联表索引
CREATE INDEX IF NOT EXISTS idx_role_menu_role_id ON t_role_menu(role_id);
CREATE INDEX IF NOT EXISTS idx_role_menu_menu_id ON t_role_menu(menu_id);

-- 6. 用户角色关联表
CREATE TABLE IF NOT EXISTS t_user_role (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_role_user_id ON t_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_user_role_role_id ON t_user_role(role_id);

-- 7. 用户组角色关联表
CREATE TABLE IF NOT EXISTS t_user_group_role (
    id BIGSERIAL PRIMARY KEY,
    user_group_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户组角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_group_role_group_id ON t_user_group_role(user_group_id);
CREATE INDEX IF NOT EXISTS idx_user_group_role_role_id ON t_user_group_role(role_id);

-- 8. 用户组成员关联表
CREATE TABLE IF NOT EXISTS t_user_group_member (
    id BIGSERIAL PRIMARY KEY,
    user_group_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    create_time TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户组成员关联表索引
CREATE INDEX IF NOT EXISTS idx_user_group_member_group_id ON t_user_group_member(user_group_id);
CREATE INDEX IF NOT EXISTS idx_user_group_member_user_id ON t_user_group_member(user_id);

-- 表字段注释
-- 1. 菜单表字段注释
COMMENT ON COLUMN t_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN t_menu.menu_name IS '菜单名称';
COMMENT ON COLUMN t_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN t_menu.menu_path IS '菜单路径';
COMMENT ON COLUMN t_menu.sort_order IS '排序号';
COMMENT ON COLUMN t_menu.menu_level IS '菜单层级';
COMMENT ON COLUMN t_menu.create_time IS '创建时间';
COMMENT ON COLUMN t_menu.update_time IS '更新时间';

-- 2. 角色表字段注释
COMMENT ON COLUMN t_role.role_id IS '角色ID';
COMMENT ON COLUMN t_role.role_name IS '角色名称';
COMMENT ON COLUMN t_role.role_description IS '角色描述';
COMMENT ON COLUMN t_role.role_type IS '角色类型：DEFAULT-默认角色，CUSTOM-自定义角色';
COMMENT ON COLUMN t_role.create_time IS '创建时间';
COMMENT ON COLUMN t_role.create_by IS '创建人';
COMMENT ON COLUMN t_role.update_time IS '更新时间';
COMMENT ON COLUMN t_role.update_by IS '更新人';

-- 3. 用户表字段注释
COMMENT ON COLUMN t_user.user_id IS '用户ID';
COMMENT ON COLUMN t_user.username IS '用户名';
COMMENT ON COLUMN t_user.user_code IS '用户工号';
COMMENT ON COLUMN t_user.organization IS '组织机构';
COMMENT ON COLUMN t_user.create_time IS '创建时间';
COMMENT ON COLUMN t_user.create_by IS '创建人';
COMMENT ON COLUMN t_user.update_time IS '更新时间';
COMMENT ON COLUMN t_user.update_by IS '更新人';

-- 4. 用户组表字段注释
COMMENT ON COLUMN t_user_group.user_group_id IS '用户组ID';
COMMENT ON COLUMN t_user_group.group_name IS '用户组名称';
COMMENT ON COLUMN t_user_group.group_description IS '用户组描述';
COMMENT ON COLUMN t_user_group.create_time IS '创建时间';
COMMENT ON COLUMN t_user_group.create_by IS '创建人';
COMMENT ON COLUMN t_user_group.update_time IS '更新时间';
COMMENT ON COLUMN t_user_group.update_by IS '更新人';

-- 5. 角色菜单关联表字段注释
COMMENT ON COLUMN t_role_menu.id IS '主键ID';
COMMENT ON COLUMN t_role_menu.role_id IS '角色ID';
COMMENT ON COLUMN t_role_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN t_role_menu.create_time IS '创建时间';

-- 6. 用户角色关联表字段注释
COMMENT ON COLUMN t_user_role.id IS '主键ID';
COMMENT ON COLUMN t_user_role.user_id IS '用户ID';
COMMENT ON COLUMN t_user_role.role_id IS '角色ID';
COMMENT ON COLUMN t_user_role.create_time IS '创建时间';

-- 7. 用户组角色关联表字段注释
COMMENT ON COLUMN t_user_group_role.id IS '主键ID';
COMMENT ON COLUMN t_user_group_role.user_group_id IS '用户组ID';
COMMENT ON COLUMN t_user_group_role.role_id IS '角色ID';
COMMENT ON COLUMN t_user_group_role.create_time IS '创建时间';

-- 8. 用户组成员关联表字段注释
COMMENT ON COLUMN t_user_group_member.id IS '主键ID';
COMMENT ON COLUMN t_user_group_member.user_group_id IS '用户组ID';
COMMENT ON COLUMN t_user_group_member.user_id IS '用户ID';
COMMENT ON COLUMN t_user_group_member.create_time IS '创建时间';


