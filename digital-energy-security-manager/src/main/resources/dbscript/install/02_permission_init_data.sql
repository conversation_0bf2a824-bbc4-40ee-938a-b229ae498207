-- 权限管理系统初始化数据
-- 支持幂等性执行

-- 1. 初始化默认角色
INSERT INTO t_role (role_name, role_description, role_type, create_by)
SELECT '系统管理员', '系统管理员，拥有所有权限', 'DEFAULT', 'system'
WHERE NOT EXISTS (SELECT 1 FROM t_role WHERE role_name = '系统管理员');

INSERT INTO t_role (role_name, role_description, role_type, create_by)
SELECT '访客', '访客角色，仅可访问首页', 'DEFAULT', 'system'
WHERE NOT EXISTS (SELECT 1 FROM t_role WHERE role_name = '访客');

INSERT INTO t_role (role_name, role_description, role_type, create_by)
SELECT '平台用户', '平台用户，可使用非权限管理菜单功能', 'DEFAULT', 'system'
WHERE NOT EXISTS (SELECT 1 FROM t_role WHERE role_name = '平台用户');

-- 2. 初始化基础菜单结构
-- 根菜单
INSERT INTO t_menu (menu_name, parent_id, menu_path, sort_order, menu_level)
SELECT '首页', 0, '/home', 1, 1
WHERE NOT EXISTS (SELECT 1 FROM t_menu WHERE menu_name = '首页');

INSERT INTO t_menu (menu_name, parent_id, menu_path, sort_order, menu_level)
SELECT '权限管理', 0, '/permission', 2, 1
WHERE NOT EXISTS (SELECT 1 FROM t_menu WHERE menu_name = '权限管理');

INSERT INTO t_menu (menu_name, parent_id, menu_path, sort_order, menu_level)
SELECT '数据管理', 0, '/data', 3, 1
WHERE NOT EXISTS (SELECT 1 FROM t_menu WHERE menu_name = '数据管理');

INSERT INTO t_menu (menu_name, parent_id, menu_path, sort_order, menu_level)
SELECT '系统设置', 0, '/system', 4, 1
WHERE NOT EXISTS (SELECT 1 FROM t_menu WHERE menu_name = '系统设置');

-- 权限管理子菜单
INSERT INTO t_menu (menu_name, parent_id, menu_path, sort_order, menu_level)
SELECT '角色管理',
    (SELECT id FROM t_menu WHERE menu_name = '权限管理'),
    '/permission/role', 1, 2
WHERE NOT EXISTS (SELECT 1 FROM t_menu WHERE menu_name = '角色管理');

INSERT INTO t_menu (menu_name, parent_id, menu_path, sort_order, menu_level)
SELECT '用户管理',
    (SELECT id FROM t_menu WHERE menu_name = '权限管理'),
    '/permission/user', 2, 2
WHERE NOT EXISTS (SELECT 1 FROM t_menu WHERE menu_name = '用户管理');

INSERT INTO t_menu (menu_name, parent_id, menu_path, sort_order, menu_level)
SELECT '用户组管理',
    (SELECT id FROM t_menu WHERE menu_name = '权限管理'),
    '/permission/user-group', 3, 2
WHERE NOT EXISTS (SELECT 1 FROM t_menu WHERE menu_name = '用户组管理');

-- 3. 为默认角色分配菜单权限
-- 系统管理员拥有所有菜单权限
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.id, m.id
FROM t_role r, t_menu m
WHERE r.role_code = 'deptAdmin' 
  AND NOT EXISTS (
    SELECT 1 FROM t_role_menu rm 
    WHERE rm.role_id = r.id AND rm.menu_id = m.id
  );

-- 访客只能访问首页
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.id, m.id
FROM t_role r, t_menu m
WHERE r.role_code = 'guest' 
  AND m.menu_code = 'home'
  AND NOT EXISTS (
    SELECT 1 FROM t_role_menu rm 
    WHERE rm.role_id = r.id AND rm.menu_id = m.id
  );

-- 平台用户可以访问除权限管理外的所有菜单
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.id, m.id
FROM t_role r, t_menu m
WHERE r.role_code = 'platform_user' 
  AND m.menu_code NOT IN ('permission_management', 'role_management', 'user_management', 'user_group_management')
  AND NOT EXISTS (
    SELECT 1 FROM t_role_menu rm 
    WHERE rm.role_id = r.id AND rm.menu_id = m.id
  );

-- 4. 创建序列（如果数据库需要）
-- PostgreSQL 使用 BIGSERIAL 会自动创建序列，这里仅作为示例
-- CREATE SEQUENCE IF NOT EXISTS seq_menu_id START 1000;
-- CREATE SEQUENCE IF NOT EXISTS seq_role_id START 1000;
-- CREATE SEQUENCE IF NOT EXISTS seq_user_id START 1000;
-- CREATE SEQUENCE IF NOT EXISTS seq_user_group_id START 1000;

-- 5. 添加注释说明
COMMENT ON TABLE t_menu IS '菜单表，存储系统菜单结构';
COMMENT ON TABLE t_role IS '角色表，存储系统角色信息';
COMMENT ON TABLE t_user IS '用户表，存储系统用户信息';
COMMENT ON TABLE t_user_group IS '用户组表，存储用户组信息';
COMMENT ON TABLE t_role_menu IS '角色菜单关联表，存储角色与菜单的权限关系';
COMMENT ON TABLE t_user_role IS '用户角色关联表，存储用户与角色的分配关系';
COMMENT ON TABLE t_user_group_role IS '用户组角色关联表，存储用户组与角色的分配关系';
COMMENT ON TABLE t_user_group_member IS '用户组成员关联表，存储用户组与用户的成员关系';

