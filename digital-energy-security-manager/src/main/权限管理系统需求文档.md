# 权限管理系统需求文档

---

## 1. 功能概述

为提升系统安全性和权限控制能力，新增“权限管理”模块，包含以下两大核心功能：

- **角色管理**：支持默认角色与自定义角色的创建、分配与维护。
- **用户（组）管理**：支持用户、用户组的创建与权限分配，实现基于角色的访问控制（RBAC）。

---

## 2. 菜单结构

### 2.1 顶级菜单
- **权限管理**

### 2.2 子菜单
- **角色管理**
- **用户（组）管理**

---

## 3. 角色管理

### 3.1 默认角色

系统预置以下三个默认角色，不可删除或重命名：

| 角色名称 | 角色标识 | 权限说明 | 特殊规则 |
|----------|----------|----------|----------|
| 系统管理员 | `detpAdmin` | 可访问所有菜单，操作不受限 | 不可删除；可通过该账户授权/解除其他用户的管理员身份 |
| 平台用户 | - | 可使用除“权限管理”外的所有平台功能 | - |
| 访客 | - | 仅可访问首页 | 首页显示欢迎标语，后续支持自定义统计信息（如：已维护局点数量等） |

> ⚠️ **说明**：`detpAdmin` 为唯一超级管理员账户，具备最高权限，系统初始化时自动创建。

### 3.2 自定义角色

支持创建自定义角色，用于精细化权限控制。

#### 3.2.1 创建规则
- **最大数量**：1000 个
- **角色名称**：长度 ≤ 20 个字符
- **权限配置**：必须为角色分配可访问的菜单权限（基于菜单树勾选）
- **状态管理**：支持启用/禁用

#### 3.2.2 管理功能
- 支持创建、修改、删除（仅限非关联角色）
- 支持修改菜单权限
- 支持查看该角色关联的用户及用户组列表
- 支持批量操作（如批量删除、批量启用）

> ❗ 删除限制：若角色已被用户或用户组引用，则禁止删除，需先解除关联。

---

## 4. 用户（组）管理

### 4.1 用户管理

#### 4.1.1 添加用户
- **身份验证**：仅允许添加中兴及中兴子公司员工，通过 UAC 接口校验用户合法性。
- **角色分配**：
  - 每个用户最多可直接分配 **10 个自定义角色**。
  - 不计入用户组继承的角色数量。
- **权限计算**：用户实际权限 = 直接分配角色权限 ∪ 用户组继承角色权限（权限并集）

#### 4.1.2 查看权限
- 提供“权限详情”视图，展示用户当前拥有的所有菜单权限。
- 显示权限来源（直接分配 or 用户组继承）。

#### 4.1.3 删除用户
- 删除用户时，自动从所有所属用户组中移除。
- 若用户为用户组管理员，需提示并移交管理权（可选扩展）。

---

### 4.2 用户组管理

#### 4.2.1 创建用户组
- **最大数量**：1000 个
- **命名规则**：长度 ≤ 20 个字符（建议）
- **成员来源**：从系统已存在用户中选择添加

#### 4.2.2 角色分配
- 每个用户组最多可分配 **10 个角色**
- 用户组成员自动继承该组的所有角色权限

#### 4.2.3 管理功能
- 支持添加/移除成员
- 支持修改用户组关联的角色
- 支持查看用户组权限并集（所有角色权限合并）
- 支持查看组内成员列表及成员权限来源

#### 4.2.4 删除用户组
- 删除用户组时，自动解除所有成员的组内关系（不删除用户本身）
- 建议增加二次确认提示

#### 4.2.5 修改用户组
- 支持修改名称、成员、角色分配等信息
- 修改后权限实时生效

---

## 5. 界面设计要求

### 5.1 角色管理界面
- 展示所有角色（默认 + 自定义）
- 提供角色权限矩阵视图（菜单树勾选）
- 支持搜索、分页、批量操作（启用/禁用/删除）
- 点击角色可查看关联的用户和用户组列表

### 5.2 用户管理界面
- 用户列表支持按姓名、工号、角色筛选
- 展示用户直接分配的角色及所属用户组
- 提供“权限详情”按钮，展示权限并集及来源路径
- 支持导入/导出用户（可选）

### 5.3 用户组管理界面
- 展示用户组列表及成员数量
- 显示用户组关联的角色及权限概览
- 支持组内成员的增删改查
- 支持拖拽或批量添加成员

---

## 6. 非功能性需求

### 6.1 性能要求
- 在 100 并发用户下，页面响应时间 < 2 秒
- 加载万级用户数据时，列表加载时间 < 3 秒（建议分页 + 懒加载）

### 6.2 安全要求
- 敏感操作（如删除角色、解除管理员权限）需二次确认
- 所有权限变更操作需记录操作日志（操作人、时间、变更内容）
- 接口需进行权限校验，防止越权访问

### 6.3 限制条件
- 默认角色（系统管理员、访客、平台用户）不可删除、不可重命名
- 用户组最多绑定 10 个角色
- 自定义角色最多创建 1000 个
- 用户组最多创建 1000 个
- 用户直接分配角色上限为 10 个（不含继承）

---

## 3. 页面功能详解

### 3.1 角色管理

- **页面布局**：
    - 左侧树形菜单展示角色管理菜单(前端直接展示)
    - 右侧分页列表展示角色详情
- **功能按钮**：
    - 新建
    - 批量删除
- **数据展示**：
    - 角色名称
    - 角色描述
    - 创建时间
    - 创建人
    - 操作(详情/编辑/删除)

### 3.2 用户管理

- **页面布局**：
    - 左侧树形菜单展示用户(组)管理两个菜单(前端直接展示)
    - 右侧分页列表展示用户管理/用户组管理(前端直接展示)
- **功能按钮**：
    - 新增
    - 批量删除
- **数据展示**：
    - 用户名(工号)
    - 组织
    - 创建时间
    - 操作(详情/编辑/删除)

## 4. 页面按钮功能

### 4.1 角色管理
- **新增按钮**：
    - 输入角色名称
    - 输入角色描述
    - 选择菜单权限
    - 点击确定/取消
- **编辑按钮**：
    - 显示角色名称(禁止修改角色名称)
    - 修改角色描述
    - 修改菜单权限
    - 点击确定/取消
- **删除按钮**：
    - 点击删除按钮
    - 弹出确认框
    - 点击确认/取消
- **详情按钮**：
    - 显示角色名称
    - 显示角色描述
    - 显示菜单权限

### 4.2 用户管理
- **新增按钮**：
    - 输入用户名
    - 调用接口查询用户组织
    - 选择关联角色(可多选)
    - 根据选中的关联角色自动查询分配的菜单权限
    - 点击确定/取消
- **编辑按钮**：
    - 显示用户名(禁止修改用户名)
    - 显示组织(禁止修改组织)
    - 修改关联角色
    - 根据选中的关联角色自动查询分配的菜单权限
    - 点击确定/取消
- **删除按钮**：
    - 点击删除按钮
    - 弹出确认框
- **详情按钮**：
    - 显示用户名
    - 显示组织
    - 显示关联角色
    - 显示分配的菜单权限
  
### 4.3 用户组管理
- **新增按钮**：
    - 输入用户组名称
    - 输入用户组描述
    - 输入用户名
    - 选择关联角色(可多选)
    - 根据选中的关联角色自动查询分配的菜单权限
    - 点击确定/取消
- **编辑按钮**：
    - 显示用户组名称(禁止修改用户组名称)
    - 修改用户组描述
    - 修改关联用户
    - 修改关联角色
    - 根据选中的关联角色自动查询分配的菜单权限
    - 点击确定/取消
- **删除按钮**：
    - 点击删除按钮
    - 弹出确认框
- **详情按钮**：
    - 显示用户组名称
    - 显示用户组描述
    - 显示关联用户
    - 显示关联角色
    - 显示分配的菜单权限

## 5.功能注意点
1. 所有查询菜单权限的地方全都是树形结构显示多层级菜单，必须查所有已存在的菜单权限，用单独字段控制是否勾选(判断是否有权限)
2. 菜单无管理页面，手动提前把数据存到数据库中，然后用关联关系表跟用户/角色/用户组关联，每次新增菜单手动存到菜单表中
3. 菜单表menu_id使用层级数字编码，编码格式：每层使用2位数字表示，层级递增时追加数字，最顶层有一个根目录，但是页面不展示，直接从第一层级开始展示
   - 第1层：01, 02, 03, 04...
   - 第2层：0101, 0102, 0201, 0202...
   - 第3层：010101, 010102, 010201...
   - 第4层：01010101, 01010102...
